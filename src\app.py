from flask import (
    Flask,
    redirect,
    request,
    jsonify,
    render_template,
    send_from_directory,
    session,
    url_for,
)
from drivers._driver_factory import DriverFactory
from core.license_manager import LicenseManager
from core.webhook_manager import WebhookManager
from core.config_manager import ConfigManager
from core.storage_manager import StorageManager
from core.task_manager import TaskManager
from core.proxy_server import ProxyServer
from core.log_manager import LogManager
from utils.rename import <PERSON><PERSON>ena<PERSON>
from flask_minify import Minify, decorators as minify_decorators
from dotenv import load_dotenv
from datetime import timedelta
from utils import utils
from core.reverse_proxy import ReverseProxy
import hashlib
import base64
import sys
import os


print(
    r"""
   ____               __  ______
  / __/_ _  ___ _____/ /_/ __/ /_______ _
 _\ \/  ' \/ _ `/ __/ __/\ \/ __/ __/  ' \
/___/_/_/_/\_,_/_/  \__/___/\__/_/ /_/_/_/

      ------  By: Cp0204  ------
 """
)
# 缓冲区刷新，输出 banner
sys.stdout.flush()


# 加载环境变量
load_dotenv()

# 环境变量配置
APP_NAME = "SmartStrm"
VERSION = utils.get_app_ver()
LICENSE = os.getenv("LICENSE", "")
CONFIG_PATH = "config/config.yaml"
PORT = os.getenv("PORT", 8024)
USERNAME = os.getenv("ADMIN_USERNAME", "admin")
PASSWORD = os.getenv("ADMIN_PASSWORD", "admin123")
DEBUG = os.getenv("DEBUG", "false").lower() == "true"
SECRET_KEY = os.getenv("SECRET_KEY", "smartstrm")
LICENSE_SERVER = os.getenv(
    "LICENSE_SERVER",
    base64.b64decode("aHR0cHM6Ly9saWNlbnNlcnZlci4weDY5Lndpbg==").decode(),
)

# 日志管理器
log_manager = LogManager()
logger = log_manager.get_logger(__name__)

# 许可管理器
license_manager = LicenseManager(LICENSE_SERVER, LICENSE, APP_NAME, VERSION)

# 初始化管理器
config_manager = ConfigManager(CONFIG_PATH)
task_manager = TaskManager(config_manager)
storage_manager = StorageManager(config_manager)
webhook_manager = WebhookManager(config_manager, task_manager)

# 代理服务器
proxy_server = ProxyServer(CONFIG_PATH)

# 初始化 Flask
app = Flask(__name__)
app.secret_key = SECRET_KEY
app.config["SESSION_COOKIE_NAME"] = "smartstrm"
app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(days=31)
app.config["MINIFY_HTML"] = True
app.jinja_env.variable_start_string = "[["
app.jinja_env.variable_end_string = "]]"
app.json.ensure_ascii = False
app.json.sort_keys = False
# 初始化 Minify
Minify(app=app, passive=True)

# 反向代理处理器
reverse_proxy = ReverseProxy()


# 获取登录令牌
def get_login_token():
    string = f"TOKEN{SECRET_KEY}{USERNAME}{PASSWORD}+-*/"
    md5 = hashlib.md5()
    md5.update(string.encode("utf-8"))
    return md5.hexdigest()[8:24]


# 登录验证装饰器
def login_required(f):
    def decorated_function(*args, **kwargs):
        login_token = get_login_token()
        if (
            session.get("token") == login_token
            or request.args.get("token") == login_token
        ):
            return f(*args, **kwargs)
        if request.headers.get("Accept") == "application/json":
            return jsonify({"error": "未登录"}), 401
        else:
            return redirect(url_for("login"))

    decorated_function.__name__ = f.__name__
    return decorated_function


@app.route("/favicon.ico")
def favicon():
    return send_from_directory(
        os.path.join(app.root_path, "static"),
        "favicon.ico",
        mimetype="image/vnd.microsoft.icon",
    )


@app.route("/login", methods=["GET", "POST"])
@minify_decorators.minify(html=True, js=True, cssless=True)
def login():
    if request.method == "GET":
        return render_template("login.html", error=None)

    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")
        if username == USERNAME and password == PASSWORD:
            session["token"] = get_login_token()
            return redirect(url_for("index"))

    return render_template("login.html", error="登录失败")


@app.route("/api/logout", methods=["POST"])
def logout():
    session.pop("token", None)
    return jsonify({"success": True})


@app.route("/")
@minify_decorators.minify(html=True, js=True, cssless=True)
@login_required
def index():
    return render_template("index.html")


@app.route("/api/run", methods=["POST"])
@login_required
def run_now():
    """立即运行所有任务"""
    try:
        tasks = config_manager.get_tasks()
        for task in tasks:
            task_manager.run_task_async(task["name"])
        return jsonify({"message": "所有任务已启动"})
    except Exception as e:
        logger.error(f"启动任务失败: {e}")
        return jsonify({"error": "启动任务失败"}), 500


# 处理 webhook 请求
@app.route("/webhook/<token>", methods=["GET", "POST"])
def webhook(token):
    """Webhook 接口"""
    if token != get_login_token():
        return jsonify({"success": False, "message": "token error"}), 401
    try:
        # 使用 Webhook 管理器处理请求
        result = webhook_manager.handle(request)
        return jsonify(result), 200
    except Exception as e:
        logger.error(f"处理 webhook 请求失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500


# 处理 STRM 请求，实现302跳转
@app.route("/smartstrm/<storage_name>/<path:file_path>")
def handle_strm(storage_name, file_path):
    """处理STRM请求，只做302跳转"""
    try:
        logger.debug(
            f"处理STRM请求: storage_name={storage_name}, file_path={file_path}"
        )
        # 获取对应存储
        storage = storage_manager.get_storage(storage_name)
        if not storage:
            return f"No storage found: {storage_name}", 404
        # # 由存储响应STRM
        # return storage.handle_strm_request(request)
        # 获取下载链接
        file_info = {"download_ua": request.headers.get("User-Agent")}
        url = storage.get_download_url(f"/{file_path}", file_info)
        if not url:
            return "Failed to get download URL", 404
        return redirect(url, code=302)
    except Exception as e:
        logger.error(f"处理STRM请求失败: {e}")
        return str(e), 500


# 处理流式代理请求
@app.route("/proxy/<storage_name>/<path:file_path>")
def handle_proxy(storage_name, file_path):
    """处理流式代理请求"""
    try:
        logger.debug(
            f"处理代理请求: storage_name={storage_name}, file_path={file_path}"
        )
        # 获取对应存储
        storage = storage_manager.get_storage(storage_name)
        if not storage:
            return f"No storage found: {storage_name}", 404

        # 获取下载信息
        file_info = {"download_ua": request.headers.get("User-Agent")}
        download_info = storage.get_download_http(f"/{file_path}", file_info)

        if not download_info or "url" not in download_info:
            return "Failed to get download URL", 404

        # 使用反向代理处理器转发请求
        target_url = download_info["url"]
        headers = download_info.get("headers", {})

        return reverse_proxy.handle_proxy_request(target_url, headers)
    except Exception as e:
        logger.error(f"处理代理请求失败: {e}")
        return str(e), 500


# 存储管理 API
@app.route("/api/storages", methods=["GET"])
@login_required
def get_storages():
    """获取所有存储配置"""
    return jsonify(config_manager.get_storages())


@app.route("/api/drivers/available", methods=["GET"])
@login_required
def get_available_drivers():
    """获取所有可用的驱动信息"""
    return jsonify(DriverFactory.get_available_drivers())


@app.route("/api/storages/<name>", methods=["POST"])
@login_required
def update_storage(name):
    """更新存储配置"""
    try:
        storage_config = request.json
        if not storage_config:
            return jsonify({"message": "错误：缺少存储配置"}), 400
        # 获取存储类型
        driver_type = storage_config.get("driver", "").lower()
        # 获取存储配置定义
        storage_info = next(
            (
                d
                for d in DriverFactory.get_available_drivers()
                if d["type"] == driver_type
            ),
            None,
        )
        if not storage_info:
            return jsonify({"message": f"不支持的驱动类型: {driver_type}"}), 400
        # 构建基础配置
        filtered_config = {"name": storage_config["name"], "driver": driver_type}
        # 根据存储配置定义过滤参数
        for key in storage_info["config"].keys():
            if key in storage_config:
                filtered_config[key] = storage_config[key]
        # 更新存储配置
        if config_manager.update_storage(name, filtered_config):
            storage_manager.reload_storage(name)
            task_manager.reload_tasks()
            return jsonify({"message": "更新存储配置成功"})
        return jsonify({"message": "更新存储配置失败"}), 500
    except Exception as e:
        logger.error(f"更新存储配置失败: {e}")
        return jsonify({"message": "更新存储配置失败"}), 500


@app.route("/api/storages/<name>", methods=["DELETE"])
@login_required
def delete_storage(name):
    """删除存储

    Args:
        name: 存储名称

    Returns:
        tuple: (响应数据, 状态码)
    """
    # 检查存储是否被任务使用
    tasks = config_manager.get_tasks()
    for task in tasks:
        if task["storage"] == name:
            return (
                jsonify(
                    {
                        "message": f"存储正在被任务 '{task['name']}' 使用，无法删除",
                    }
                ),
                400,
            )
    # 删除存储
    if config_manager.delete_storage(name):
        return jsonify({"message": "存储已删除"})
    return jsonify({"message": "删除存储失败"}), 500


@app.route("/api/storage/<name>/test", methods=["POST"])
@login_required
def test_storage(name):
    """测试存储连接"""
    try:
        storage = storage_manager.get_storage(name)
        if not storage:
            return (
                jsonify({"success": False, "message": f"错误：存储不存在 {name}"}),
                400,
            )
        # 尝试读取文件列表
        result = storage.list_files("/")
        if not result["success"]:
            return jsonify(result), 400
        data = result["data"]
        return jsonify({"success": True, "message": "连接成功", "data": data})
    except Exception as e:
        logger.error(f"测试存储连接失败: {e}")
        return jsonify({"success": False, "message": f"连接失败: {str(e)}"}), 500


@app.route("/api/storage/<name>/browse", methods=["GET"])
@login_required
def browse_storage(name):
    """浏览存储文件"""
    try:
        path = request.args.get("path", "/")
        rename_mode = request.args.get("rename_mode", "")
        storage = storage_manager.get_storage(name)
        if not storage:
            return (
                jsonify({"success": False, "message": f"错误：存储不存在 {name}"}),
                400,
            )
        # 获取文件列表
        result = storage.list_files(path)
        if not result["success"]:
            return jsonify(result), 200
        files = result["data"]
        # 应用重命名规则
        if rename_mode == "magic":
            settings = config_manager.get_settings()
            mr = MagicRename()
            mr.set_regex(settings["rename"]["movie"], settings["rename"]["tv"])
            files = mr.magic_rename_files(files, path)
        return jsonify(
            {"success": True, "path": path, "rename_mode": rename_mode, "files": files}
        )
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/api/storage/<name>/rename", methods=["POST"])
@login_required
def rename_file(name):
    """重命名文件"""
    try:
        data = request.get_json()
        files = data.get("files", [])
        if not files:
            return jsonify({"success": False, "message": "缺少必要参数"}), 400
        storage = storage_manager.get_storage(name)
        if not storage:
            return (
                jsonify({"success": False, "message": f"错误：存储不存在 {name}"}),
                400,
            )
        for file in files:
            if file["new_name"] == os.path.basename(file["path"]):
                continue
            if not storage.rename_file(file["path"], file["new_name"]):
                return (
                    jsonify(
                        {"success": False, "message": f"重命名失败: {file['path']}"}
                    ),
                    500,
                )
        return jsonify({"success": True, "message": "重命名成功"})
    except Exception as e:
        logger.error(f"重命名文件失败: {e}")
        return jsonify({"success": False, "message": f"重命名失败: {str(e)}"}), 500


@app.route("/api/storage/<name>/delete", methods=["POST"])
@login_required
def delete_file(name):
    """删除文件"""
    try:
        data = request.get_json()
        path = data.get("path")
        if not path:
            return jsonify({"success": False, "message": "缺少必要参数"}), 400
        storage = storage_manager.get_storage(name)
        if not storage:
            return (
                jsonify({"success": False, "message": f"错误：存储不存在 {name}"}),
                400,
            )
        if storage.delete_file(path):
            return jsonify({"success": True, "message": "删除成功"})
        else:
            return jsonify({"success": False, "message": "删除失败"}), 500
    except Exception as e:
        logger.error(f"删除文件失败: {e}")
        return jsonify({"success": False, "message": f"删除失败: {str(e)}"}), 500


# 任务管理 API
@app.route("/api/tasks", methods=["GET"])
@login_required
def get_tasks():
    """获取所有任务状态"""
    return jsonify(task_manager.get_all_tasks_status())


@app.route("/api/tasks/<name>", methods=["POST"])
@login_required
def update_task(name):
    """更新任务配置"""
    data = request.get_json()
    if config_manager.update_task(name, data) and task_manager.update_task(name, data):
        return jsonify({"success": True, "message": "任务已更新"})
    return jsonify({"success": False, "message": "更新任务失败"}), 500


@app.route("/api/tasks/<name>", methods=["DELETE"])
@login_required
def delete_task(name):
    """删除任务"""
    if config_manager.delete_task(name) and task_manager.delete_task(name):
        return jsonify({"success": True})
    return jsonify({"success": False, "message": "删除任务失败"}), 500


@app.route("/api/tasks/<name>/run", methods=["POST"])
@login_required
def run_task(name):
    """运行任务"""
    if task_manager.run_task_async(name):
        return jsonify({"success": True, "message": "任务已启动"})
    return jsonify({"success": False, "message": "启动任务失败"}), 500


@app.route("/api/tasks/<task_name>/log")
@login_required
def get_task_log(task_name):
    """获取任务日志"""
    try:
        # 从日志文件中读取任务日志
        log_file = os.path.join("logs", "tasks", f"{task_name}.log")
        if not os.path.exists(log_file):
            return jsonify({"log": "日志文件不存在"})

        with open(log_file, "r", encoding="utf-8") as f:
            log_lines = f.readlines()

        return jsonify({"log": "".join(log_lines[-1000:])})  # 只返回最后1000行
    except Exception as e:
        return jsonify({"error": str(e)}), 500


# 系统设置 API
@app.route("/api/settings", methods=["GET"])
@login_required
def get_settings():
    """获取系统设置"""
    try:
        settings = config_manager.get_settings()
        # Webhook
        settings["webhook"]["url"] = url_for(
            "webhook", token=get_login_token(), _external=True
        )
        # 代理服务器状态
        settings["proxy"]["is_running"] = proxy_server.is_running()
        hostname = request.host.split(":")[0]
        settings["proxy"]["url"] = f"http://{hostname}:{settings['proxy']['port']}"
        settings["proxy"]["title"] = proxy_server.config.get("title", "")
        # 许可功能
        if not license_manager.is_licensed("proxy_server"):
            settings["proxy"]["enabled"] = False
        return jsonify(settings)
    except Exception as e:
        logger.error(f"获取系统设置失败: {e}")
        return jsonify({"message": "获取系统设置失败"}), 500


@app.route("/api/version", methods=["GET"])
def get_version():
    return jsonify({"version": VERSION})


@app.route("/api/settings", methods=["POST"])
@login_required
def update_settings():
    """更新系统设置"""
    try:
        settings = request.json
        if not settings:
            return jsonify({"message": "错误：缺少设置数据"}), 400
        # 代理服务器状态
        if proxy_config := settings.get("proxy", {}):
            # 许可功能
            if not license_manager.is_licensed("proxy_server"):
                proxy_config["enabled"] = False
            success, message = proxy_server.update_config(proxy_config)
            if not success:
                return jsonify({"message": message}), 500
        # 更新设置
        if config_manager.update_settings(settings):
            # 仅当 strm 设置改变时重载任务
            if "strm" in settings:
                task_manager.reload_tasks()
            return jsonify({"message": "更新系统设置成功"})
        return jsonify({"message": "更新系统设置失败"}), 500
    except Exception as e:
        logger.error(f"更新系统设置失败: {e}")
        return jsonify({"message": "更新系统设置失败"}), 500


# 许可信息 API
@app.route("/api/license", methods=["GET"])
@login_required
def get_license_info():
    """获取许可信息"""
    try:
        license_info = license_manager.get_license_info()
        info = {
            "email": license_info.get("email"),
            "plan": license_info.get("plan"),
            "expires_str": license_info.get("expires_str"),
            "valid": license_info.get("valid"),
            "online": license_info.get("online"),
        }
        # info = {
        #     "email": "<EMAIL>",
        #     "plan": "admin",
        #     "expires_str": "forever",
        #     "valid": False,
        #     "online": False,
        # }
        return jsonify(info), 200
    except Exception as e:
        logger.error(f"获取许可信息失败: {e}")
        return jsonify({"message": "获取许可信息失败"}), 500


@app.route("/api/proxy/status", methods=["GET"])
@login_required
def get_proxy_status():
    """获取代理服务器状态"""
    return jsonify(
        {
            "running": proxy_server.is_running(),
            "config": config_manager.get_settings().get("proxy", {}),
        }
    )


def main():
    """主函数"""
    # 隐藏 Flask 启动信息
    sys.modules["flask.cli"].show_server_banner = lambda *x: None

    # 启动代理服务器（主进程）
    if not DEBUG or os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        # 初始化许可验证
        license_manager.init()
        # 启动代理服务器
        enabled = config_manager.get_settings().get("proxy", {}).get("enabled", False)
        if enabled and license_manager.is_licensed("proxy_server"):
            proxy_server.start()

    # 启动 Flask 应用
    app.run(host="0.0.0.0", port=PORT, debug=DEBUG)


if __name__ == "__main__":
    main()
