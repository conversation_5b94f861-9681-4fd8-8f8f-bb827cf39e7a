"""
Flask 反向代理模块
提供高性能的流式传输代理功能
"""

import requests
from flask import Response, request, stream_with_context
from typing import Dict, Any, Generator, Optional
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)


class ReverseProxy:
    """Flask 反向代理处理器"""

    def __init__(self):
        """初始化反向代理处理器"""
        pass

    def _generate_stream_with_range(
        self, url: str, headers: Dict[str, str], file_size: int
    ) -> Generator[bytes, None, None]:
        """生成带Range的流式响应数据

        Args:
            url: 目标URL
            headers: 请求头
            file_size: 文件大小

        Yields:
            bytes: 响应数据块
        """
        try:
            # 获取客户端的Range请求头
            range_header = request.headers.get("Range")
            
            start, end = 0, file_size
            is_partial = False
            
            if range_header:
                # 解析Range头，格式如"bytes=0-1023"
                if range_header.startswith("bytes="):
                    range_str = range_header[6:]  # 去掉"bytes="前缀
                    if "-" in range_str:
                        start_str, end_str = range_str.split("-", 1)
                        if start_str:
                            start = int(start_str)
                        if end_str:
                            end = int(end_str) + 1  # Range是闭区间，需要加1
                        else:
                            end = file_size
                        is_partial = True
            
            # 如果是部分内容请求，设置相应头
            if is_partial:
                content_length = end - start
                # 在这里我们无法直接设置响应头，需要在上层设置
                logger.debug(f"处理Range请求: {start}-{end-1}/{file_size}")
            else:
                logger.debug(f"处理完整文件请求: {file_size} bytes")

            # 定义每次请求的数据块大小，这里设置为 10 MB
            chunk_size = 10 * 1024 * 1024
            
            # 循环下载文件，直到所有请求的范围数据都传输完毕
            current_pos = start
            while current_pos < end:
                # 计算当前数据块的结束位置
                chunk_end = current_pos + chunk_size
                # 如果计算出的结束位置超出了请求的总结束位置，则调整为总结束位置
                if chunk_end > end:
                    chunk_end = end
                
                # 构造 Range 请求头，用于从原始文件服务器获取指定范围的数据
                range_req = f"bytes={current_pos}-{chunk_end-1}"
                current_pos = chunk_end
                
                # 创建请求头副本并添加Range头
                req_headers = headers.copy()
                req_headers["Range"] = range_req
                
                # 发起请求
                resp = requests.get(
                    url,
                    headers=req_headers,
                    stream=True,
                    allow_redirects=True,
                    timeout=(10, 30),
                )
                
                try:
                    # 检查响应状态码，期望是 206 Partial Content
                    if resp.status_code != 206:
                        logger.error(f"Unexpected status code: {resp.status_code}")
                        break
                    
                    # 将原始文件服务器返回的数据流拷贝到响应中
                    for chunk in resp.iter_content(chunk_size=8192):
                        if chunk:
                            yield chunk
                finally:
                    resp.close()
                    
        except Exception as e:
            logger.error(f"流式传输过程中发生错误: {e}")
        except GeneratorExit:
            logger.info("客户端断开连接")

    def create_proxy_response(
        self, url: str, headers: Dict[str, str] = None
    ) -> Response:
        """创建代理响应

        Args:
            url: 目标URL
            headers: 请求头

        Returns:
            Response: Flask响应对象
        """
        try:
            # 合并请求头
            req_headers = dict(request.headers)
            if headers:
                req_headers.update(headers)

            url_parse = requests.utils.urlparse(url)
            req_headers.update(
                {
                    "Host": url_parse.netloc,
                }
            )

            # 先发起一个HEAD请求获取文件信息
            head_resp = requests.head(
                url,
                headers=req_headers,
                allow_redirects=True,
                timeout=(10, 30),
            )
            
            file_size = 0
            if "content-length" in head_resp.headers:
                file_size = int(head_resp.headers["content-length"])
            
            head_resp.close()

            # 获取客户端的Range请求头
            range_header = request.headers.get("Range")
            
            start, end = 0, file_size
            is_partial = False
            
            # 解析Range头
            if range_header and range_header.startswith("bytes="):
                range_str = range_header[6:]  # 去掉"bytes="前缀
                if "-" in range_str:
                    start_str, end_str = range_str.split("-", 1)
                    if start_str:
                        start = int(start_str)
                    if end_str:
                        end = int(end_str) + 1  # Range是闭区间，需要加1
                    else:
                        end = file_size
                    is_partial = True

            # 构建响应头
            response_headers = {}
            
            if is_partial:
                content_length = end - start
                response_headers["Content-Range"] = f"bytes {start}-{end-1}/{file_size}"
                response_headers["Content-Length"] = str(content_length)
                status_code = 206  # Partial Content
            else:
                response_headers["Content-Length"] = str(file_size)
                status_code = 200  # OK

            # 创建流式响应
            response = Response(
                stream_with_context(self._generate_stream_with_range(url, req_headers, file_size)),
                status=status_code,
                headers=response_headers,
            )

            return response

        except requests.exceptions.RequestException as e:
            logger.error(f"代理请求失败: {e}")
            return Response("Proxy request failed", status=502)
        except Exception as e:
            logger.error(f"创建代理响应时发生错误: {e}")
            return Response("Internal server error", status=500)

    def handle_proxy_request(
        self, target_url: str, additional_headers: Dict[str, str] = None
    ) -> Response:
        """处理代理请求

        Args:
            target_url: 目标URL
            additional_headers: 额外的请求头

        Returns:
            Response: Flask响应对象
        """
        logger.debug(f"处理代理请求: {target_url}")

        try:
            return self.create_proxy_response(target_url, additional_headers)
        except Exception as e:
            logger.error(f"处理代理请求时发生错误: {e}")
            return Response("Internal server error", status=500)
