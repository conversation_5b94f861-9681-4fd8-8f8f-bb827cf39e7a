import os
import json
import time
import base64
import hashlib
import logging
import requests
from typing import Dict, Any, Optional
from Crypto.Cipher import AES
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256
from Crypto.Util.Padding import pad, unpad
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)


class LicenseManager:
    """许可管理器"""

    def __init__(
        self,
        license_server: str = "http://localhost:8099",
        license="",
        app_name="",
        version="dev",
    ):
        """初始化许可管理器

        Args:
            license_server_url: 许可服务器URL
        """
        self.license_server = license_server
        self.license_enc = license
        self.app_name = app_name.lower().replace(" ", "")
        self.version = version

        # 许可信息
        self.licensed = False
        self.license_info = {"valid": False}

        # AES 密钥
        a = "48a5ff6a74d33f01"
        b = "482279df223abb81"
        self.secret = f"{a}{b}"
        # RSA公钥（用于验证服务端签名）
        self.public_key_pem = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw3kabiWhjsgWJBZlnn8y
OIhbl5gzve2zptHSV2XOsFkVuCj1FX/PqNABFRq81rTBlG6Qb5I6BS+LK/s5NH+1
jIIcTs9Jj3NjSPUi7MV112pS3JnRsSIPv/oDYZI+qEscDNhIKsZ0Qe73HlI7i7FB
hErRxNTx2ELSgBKGHQHHgbUcCSs6ZPv76siPLfapgYItWmb5rOM8JnH9+Mr3Z3RF
incuuy1PCzb776Zqr2fpKBptn/Hk6bvdYtaGvqxaCzzqEOYokQzpBkjKPG2sh7u4
r8qA6aTIYq+h5TtjpizlPC0rcyNVwCJ7cg1XNCGeJ2K/j5yOLBf1px3nZKh5ywOp
dQIDAQAB
-----END PUBLIC KEY-----"""

        if not os.environ.get("DEBUG"):
            logger.setLevel(logging.INFO)

    def _aes_encrypt(self, data: str) -> str:
        """AES加密"""
        try:
            cipher = AES.new(self.secret.encode()[:32], AES.MODE_CBC)
            padded_data = pad(data.encode(), AES.block_size)
            encrypted = cipher.encrypt(padded_data)
            # 返回 IV + 加密数据的base64编码
            return base64.b64encode(cipher.iv + encrypted).decode()
        except Exception as e:
            logger.debug(f"AES加密失败: {e}")
            return ""

    def _aes_decrypt(self, encrypted_data: str) -> str:
        """AES解密"""
        try:
            data = base64.b64decode(encrypted_data)
            iv = data[:16]  # AES block size
            encrypted = data[16:]
            cipher = AES.new(self.secret.encode()[:32], AES.MODE_CBC, iv)
            decrypted = unpad(cipher.decrypt(encrypted), AES.block_size)
            return decrypted.decode()
        except Exception as e:
            logger.debug(f"AES解密失败: {e}")
            return ""

    def _verify_rsa_signature(self, data: str, signature: str) -> bool:
        """验证RSA签名"""
        try:
            public_key = RSA.import_key(self.public_key_pem)
            signature_bytes = base64.b64decode(signature)
            hash_obj = SHA256.new(data.encode())
            pkcs1_15.new(public_key).verify(hash_obj, signature_bytes)
            return True
        except Exception as e:
            logger.debug(f"RSA签名验证失败: {e}")
            return False

    def _generate_request_signature(self, params: Dict[str, Any]) -> str:
        """生成请求签名"""
        # 按key排序拼接参数
        sorted_params = sorted(params.items())
        param_str = "&".join([f"{k}={v}" for k, v in sorted_params])
        param_str += f"&{self.secret}"
        return hashlib.md5(param_str.encode()).hexdigest()

    def _load_offline_license(self) -> Optional[Dict[str, Any]]:
        """从环境变量加载许可"""
        try:
            if not self.license_enc:
                logger.debug("未找到许可证")
                return None

            # 解密环境变量中的许可信息
            decrypted = self._aes_decrypt(self.license_enc)
            if not decrypted:
                logger.debug("解密许可证失败")
                return None

            license_data = json.loads(decrypted)

            # 验证签名
            if not self._verify_rsa_signature(
                license_data["license"], license_data["signature"]
            ):
                logger.debug("许可证签名验证失败")
                return None

            # 解密许可信息
            license_json = self._aes_decrypt(license_data["license"])
            if not license_json:
                logger.debug("解密许可证信息失败")
                return None

            return json.loads(license_json)
        except Exception as e:
            logger.debug(f"加载离线许可证失败: {e}")
            return None

    def _request_online_license(
        self, license_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """请求在线许可验证"""
        try:
            # 加密许可信息
            encrypted_license = self._aes_encrypt(json.dumps(license_data))

            # 构建请求参数
            timestamp = int(time.time())
            params = {
                "license": encrypted_license,
                "version": self.version,
                "timestamp": timestamp,
            }

            # 生成签名
            signature = self._generate_request_signature(params)
            params["signature"] = signature

            # 发送请求
            response = requests.post(
                f"{self.license_server}/api/license/verify", json=params, timeout=10
            )

            if response.status_code != 200:
                logger.debug(f"在线许可验证请求失败: {response.status_code}")
                return None

            result = response.json()
            if result.get("code") != 200:
                logger.debug(f"在线许可验证失败: {result.get('message')}")
                return None

            # 验证返回的签名
            if not self._verify_rsa_signature(result["license"], result["signature"]):
                logger.debug("在线许可返回签名验证失败")
                return None

            # 解密返回的许可信息
            license_json = self._aes_decrypt(result["license"])
            if not license_json:
                logger.debug("解密在线许可信息失败")
                return None

            return json.loads(license_json)
        except Exception as e:
            logger.debug(f"在线许可验证异常: {e}")
            return None

    def _is_license_valid(self, license_data: Dict[str, Any]) -> bool:
        """检查许可是否有效"""
        current_time = int(time.time())
        expires_at = license_data.get("expires_at", 0)

        # 检查是否过期
        if current_time > expires_at:
            logger.debug("许可已过期")
            return False

        return True

    def init(self) -> bool:
        """初始化许可管理器"""
        try:
            logger.info("正在验证许可证...")

            # 从环境变量加载许可
            offline_license = self._load_offline_license()
            if not offline_license:
                logger.error("无法加载许可信息")
                return False

            # 尝试在线验证
            online_license = self._request_online_license(offline_license)

            if online_license:
                # 在线验证成功，使用在线许可
                logger.info("在线许可验证成功")
                self.license_info = online_license
                self.license_info["online"] = True
            else:
                # 在线验证失败，使用环境变量许可
                logger.warning("在线许可验证失败，使用离线许可")
                self.license_info = offline_license
                self.license_info["online"] = False

            # 检查应用标识
            if self.license_info.get("app") != self.app_name:
                logger.error("许可应用标识不匹配")
                return False

            is_valid = self._is_license_valid(self.license_info)

            self.license_info["valid"] = is_valid
            expires_at = self.license_info.get("expires_at", 0)
            self.license_info["expires_str"] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(expires_at)
            )

            logger.info(
                f"{'✅' if is_valid else '❌'} 用户: {self.license_info.get('email')} 到期时间: {self.license_info.get('expires_str')}"
            )

            self.licensed = True

            return is_valid
        except Exception as e:
            logger.error(f"许可初始化失败: {e}")
            return False

    def get_license_info(self) -> Optional[Dict[str, Any]]:
        """获取许可信息"""
        if not self.licensed:
            return {"valid": False}
        info = self.license_info.copy()
        # 动态检查是否过期
        info["valid"] = self._is_license_valid(self.license_info)
        return info

    def is_licensed(self, feature: str = "") -> bool:
        """检查特定功能是否许可"""
        license_info = self.get_license_info()
        if not license_info.get("valid", False):
            return False

        # 这里可以根据不同的许可级别控制功能
        # 目前简单返回许可是否有效
        return True
