import io
import os
from utils import utils
from typing import List, Dict, Any
from webdav3.client import Client
from ._base import BaseDriver
from datetime import datetime
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)


class WebDAVDriver(BaseDriver):
    """WebDAV 驱动类"""

    DRIVER_TYPE = "webdav"
    DRIVER_NAME = "WebDAV"

    DRIVER_CONFIG = {
        "hostname": {
            "type": "string",
            "required": True,
            "label": "WebDAV 地址",
            "placeholder": "http://example.com/webdav",
            "tip": "",
        },
        "login": {"type": "string", "required": True, "label": "用户名", "tip": ""},
        "password": {"type": "password", "required": True, "label": "密码", "tip": ""},
    }

    DRIVER_TIPS = {
        "type": "warning",
        "message": "<b>注意：</b>WebDAV 驱动 URL 拼接格式为<br><code>http://用户名:密码@WebDAV/path</code><br>有可能会泄漏你的明文密码，建议分配临时用户使用",
    }

    def __init__(self, config: Dict[str, Any]):
        """初始化 WebDAV 驱动

        Args:
            hostname: WebDAV 服务器地址
            login: 用户名
            password: 密码
        """
        required_keys = ["hostname", "login", "password"]
        for key in required_keys:
            if key not in config or not config[key]:
                raise ValueError(f"配置缺少必需项: {key}")
        self.name = None
        self.hostname = config["hostname"].rstrip("/")
        self.login = config["login"]
        self.password = config["password"]
        self.client = Client(
            {
                "webdav_hostname": self.hostname,
                "webdav_login": self.login,
                "webdav_password": self.password,
                "disable_check": True,
            }
        )

    def _convert_time(self, time_str: str) -> float:
        """转换时间格式为时间戳

        Args:
            time_str: 时间字符串

        Returns:
            float: 时间戳（秒）
        """
        if not time_str:
            return None
        try:
            dt = datetime.strptime(time_str, "%a, %d %b %Y %H:%M:%S GMT")
            return dt.timestamp()
        except ValueError:
            try:
                dt = datetime.fromisoformat(time_str.replace("Z", "+00:00"))
                return dt.timestamp()
            except ValueError:
                logger.error(f"无法解析时间格式: {time_str}")
                return None

    def list_files(self, path: str) -> Dict[str, Any]:
        """列出指定路径下的所有文件

        Args:
            path: 要扫描的路径

        Returns:
            Dict[str, Any]:
                success: 是否成功
                data: List[Dict]: 文件信息列表，每个文件包含 name, isdir, path, size, modified, created 信息，其他信息可选
                message: 错误信息
        """
        try:
            dav_files = self.client.list(path, get_info=True)
            # print(dav_files)
            # 删除第一个文件夹(根目录)
            dav_files = dav_files[1:]
            # print(json.dumps(files, ensure_ascii=False))
            files = [
                {
                    "name": f["name"],
                    "isdir": f["isdir"],
                    "path": os.path.join(path, f["name"]).replace("\\", "/"),
                    "size": int(f.get("size", 0)) if not f.get("isdir") else "",
                    "modified": self._convert_time(f.get("modified")),
                    "created": self._convert_time(f.get("created")),
                    "type": f.get("content_type"),
                }
                for f in dav_files
            ]
            return {"success": True, "data": files}
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            utils.debug_print_exc()
            return {"success": False, "message": e}

    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            self.client.clean(path)
            return True
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        try:
            res = self.client.resource(path)
            res.rename(new_name)
            return True
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            str: 下载 URL
        """
        return f"{self.hostname.split('//')[0]}//{self.login}:{self.password}@{self.hostname.split('//')[1]}{path}"

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL

        Args:
            path: 文件路径
            file_info: 文件信息
        """
        return self.get_download_url(path, file_info)

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            bytes: 文件二进制数据
        """
        try:
            res = self.client.resource(path)
            # 将文件内容读取到BytesIO缓冲区
            buffer = io.BytesIO()
            res.write_to(buffer)
            binary_data = buffer.getvalue()
            return binary_data
        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""
