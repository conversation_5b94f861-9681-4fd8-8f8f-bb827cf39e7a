.env: &env
  IMAGE_TAG: ${CNB_REPO_NAME_LOWERCASE}:${CNB_BRANCH}
  CNB_IMAGE_TAG: ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:${CNB_BRANCH}
  ALI_IMAGE_TAG: ${ALIYUN_REGISTRY}/${ALIYUN_NAMESPACE}/${IMAGE_TAG}
  HUB_IMAGE_TAG: ${DOCKERHUB_USERNAME}/${IMAGE_TAG}

.make-image-envs: &make-image-envs
  - name: set HUB_IMAGE_TARGET
    script: echo -n "$HUB_IMAGE_TAG"
    exports:
      info: HUB_IMAGE_TARGET
  - name: use latest if vX.X.X
    if: |
      [[ "${CNB_BRANCH:0:1}" = "v" ]]
    script: echo -n "${HUB_IMAGE_TAG},${DOCKERHUB_USERNAME}/${CNB_REPO_NAME_LOWERCASE}:latest"
    exports:
      info: HUB_IMAGE_TARGET

# 构建共用
.build-by-arch: &build-by-arch
  - name: docker login
    script:
      - docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
      - docker login -u ${ALIYUN_USERNAME} -p "${ALIYUN_TOKEN}" ${ALIYUN_REGISTRY}
      - docker login -u ${DOCKERHUB_USERNAME} -p "${DOCKERHUB_TOKEN}"
  # 构建镜像
  - name: docker build
    script: docker build --build-arg BUILD_SHA=${CNB_BRANCH_SHA} --build-arg BUILD_TAG=${CNB_BRANCH} -t ${IMAGE_TAG} .
  # 打标签
  - name: docker tag
    script:
      - docker tag ${IMAGE_TAG} ${CNB_IMAGE_TAG}-linux-${BUILD_ARCH}
      - docker tag ${IMAGE_TAG} ${ALI_IMAGE_TAG}-linux-${BUILD_ARCH}
      - docker tag ${IMAGE_TAG} ${HUB_IMAGE_TAG}-linux-${BUILD_ARCH}
  # 推送镜像
  - name: docker push cnb
    script: docker push ${CNB_IMAGE_TAG}-linux-${BUILD_ARCH}
  - name: docker push aliyun
    if: |
      [ "$CNB_BRANCH" = "main" ]
    script: docker push ${ALI_IMAGE_TAG}-linux-${BUILD_ARCH}
  - name: docker push dockerhub
    if: |
      [[ "${CNB_BRANCH:0:1}" = "v" ]]
    script: docker push ${HUB_IMAGE_TAG}-linux-${BUILD_ARCH}
  - name: resolve
    type: cnb:resolve
    options:
      key: build-$BUILD_ARCH

.amd64-arch-build: &amd64-arch-build
  name: build-amd64
  runner:
    tags: cnb:arch:amd64
  services:
    - docker
  imports: https://cnb.cool/Cp0204/env/-/blob/main/env.yml
  env:
    BUILD_ARCH: amd64
    <<: *env
  stages:
    - *build-by-arch

.arm64-arch-build: &arm64-arch-build
  name: build-arm64
  runner:
    tags: cnb:arch:arm64:v8
  imports: https://cnb.cool/Cp0204/env/-/blob/main/env.yml
  env:
    BUILD_ARCH: arm64
    <<: *env
  services:
    - docker
  stages:
    - *build-by-arch

# 合并多架构
.multi-arch-push: &multi-arch-push
  - name: await the amd64
    type: cnb:await
    options:
      key: build-amd64
  - name: await the arm64
    type: cnb:await
    options:
      key: build-arm64

  # 合并清单 CNB
  - name: manifest cnb
    image: cnbcool/manifest
    settings:
      target: ${CNB_IMAGE_TAG}
      template: ${CNB_IMAGE_TAG}-OS-ARCH
      platforms:
        - linux/amd64
        - linux/arm64
  # 清理 CNB
  - name: remove cnb tag
    type: artifact:remove-tag
    options:
      name: ${CNB_REPO_NAME}
      tags:
        - ${CNB_BRANCH}-linux-amd64
        - ${CNB_BRANCH}-linux-arm64
      type: docker

  # 合并清单阿里云
  - name: manifest aliyun
    if: |
      [ "$CNB_BRANCH" = "main" ]
    image: cnbcool/manifest
    settings:
      username: ${ALIYUN_USERNAME}
      password: ${ALIYUN_TOKEN}
      target: ${ALI_IMAGE_TAG}
      template: ${ALI_IMAGE_TAG}-OS-ARCH
      platforms:
        - linux/amd64
        - linux/arm64

  # 合并清单 Docker Hub
  - name: manifest dockerhub
    if: |
      [[ "${CNB_BRANCH:0:1}" = "v" ]]
    image: cnbcool/manifest
    settings:
      username: ${DOCKERHUB_USERNAME}
      password: ${DOCKERHUB_TOKEN}
      target: ${HUB_IMAGE_TARGET}
      template: ${HUB_IMAGE_TAG}-OS-ARCH
      platforms:
        - linux/amd64
        - linux/arm64
  # 清理 Docker Hub
  - name: clear dockerhub
    if: |
      [[ "${CNB_BRANCH:0:1}" = "v" ]]
    image: lumir/remove-dockerhub-tag
    args:
      - --user
      - $DOCKERHUB_USERNAME
      - --password
      - $DOCKERHUB_TOKEN
      - ${IMAGE_TAG}-linux-amd64
      - ${IMAGE_TAG}-linux-arm64

"(main|dev)":
  push:
    - *amd64-arch-build
    - *arm64-arch-build
    - name: conbine-arch
      imports: https://cnb.cool/Cp0204/env/-/blob/main/env.yml
      env:
        <<: *env
      services:
        - docker
      stages:
        - *make-image-envs
        - *multi-arch-push

$:
  tag_push:
    - *amd64-arch-build
    - *arm64-arch-build
    - name: conbine-arch
      imports: https://cnb.cool/Cp0204/env/-/blob/main/env.yml
      env:
        <<: *env
      services:
        - docker
      stages:
        - *make-image-envs
        - *multi-arch-push
