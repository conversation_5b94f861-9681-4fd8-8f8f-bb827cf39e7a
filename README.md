
# SmartStrm

SmartStrm 是一个媒体库 STRM 文件生成工具，力求打通网络存储和私有媒体库的最后一关。正如其名，它希望尽可能地帮你减少繁杂的操作。

## 部署

docker-compose.yml

```yaml
name: smartstrm
services:
  smartstrm:
    image: cp0204/smartstrm:latest
    container_name: smartstrm
    restart: unless-stopped
    network_mode: host
    volumes:
      - /yourpath/smartstrm/config:/app/config # 挂载配置目录
      - /yourpath/smartstrm/logs:/app/logs # 挂载日志目录，可选
      - /yourpath/smartstrm/strm:/strm # 挂载 STRM 生成目录
    environment:
      - PORT=8024 # 管理端口
      - ADMIN_USERNAME=admin # 管理用户名
      - ADMIN_PASSWORD=admin123 # 管理用户密码
      - LICENSE= # 许可证字符串（如有）
```

## 基础使用

1. 添加存储，支持 OpenList、WebDAV、Quark、115 等存储驱动。
2. 浏览文件，添加任务，生成STRM文件到本地。
3. 挂载 STRM 目录给媒体库容器 /strm 路径
4. 添加媒体库
5. 设置302代理服务器（Pro）

## 进阶使用

### Emby 通知

删除媒体同步删除远程存储的文件，需要挂载到 Emby 容器的 /strm 路径下

#### Jellyfin

### 转存自动生成STRM

支持和 `quark-auto-save` `CloudSaver` 联动，在转存后触发任务。并且是优雅地触发，仅触发转存的那一个文件夹，秒级生成。

#### quark-auto-save 插件


需配置项：

#### CloudSaver 插件

- **webhook**: SmartStrm Webhook 地址
- **strmtask**: SmartStrm 任务名，支持多个如 `tv,movie`
- **xlist_path_fix**: 路径映射，使用 OpenList 驱动时需填写 `/storage_mount_path:/quark_root_dir` ，例如把夸克根目录挂载在 OpenList 的 /quark 下，则填写 `/quark:/` ；以及 SmartStrm 会使 OpenList 强制刷新目录，无需再用 alist 插件刷新。 SmartStrm 任务使用 Quark 驱动时无须填写。

## Q&A

> Q: 是否支持网盘转存、自动转存？
>
> A: 暂无计划，做好一件事，少即是多。世面上已经有不少转存工具，不重复造轮子，除非我有信心做成最好用的轮子。

> Q: 免费和收费？
>
> A: 基础功能免费（STRM生成，Webhook），已经能够满足绝大多数人需求，高级功能付费（302代理）。

> Q: 为什么订阅制，可以买断吗？
>
> A: 与其一锤子买卖，我更希望它被您持续地肯定价值。我认为**以极其良心的价格订阅**，比高额买断要合理一些，好就续订、不好就不续。类似软件都可能会随接口变化而失效，即使买断了，我也无法保证能持续更新和永远能用。

## 302兼容性

> 图标示意: ✅兼容 ❌不兼容 🟡部分兼容 ⚫未测试

### 按驱动分

| 功能        | Emby 302 | Jellyfin 302 | 绿联 | 备注                                      |
| ----------- | -------- | ------------ | ---- | ----------------------------------------- |
| OpenList    | ✅        | ✅            | ✅    | 如果存储开启302，直链兼容性跟随网盘       |
| Quark       | 🟡        | 🟡            | 🟡    | 默认使用转码播放，在Web端部分视频不兼容   |
| 115开放平台 | ✅        | ✅            | ⚫    | 直链完美兼容，但普通用户API限速，频繁报错 |

#### 按客户端分

| 客户端     | Emby | Jellyfin | 绿联 | 备注       |
| ---------- | ---- | -------- | ---- |
| Web        | 🟡    | 🟡        | 🟡    |
| Android    | ✅    | ✅        | ⚫    |
| Android TV | ✅    | -        |      |
| VidHub     | ✅    | ✅        |      |
| Yamby      | 🟡    | -        |      | 不支持m3u8 |
| AfuseKt    | ✅    | ✅        |      |