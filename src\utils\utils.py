import os
import traceback

DEBUG = os.getenv("DEBUG", "").lower() == "true"


def get_app_ver():
    BUILD_SHA = os.getenv("BUILD_SHA", "")
    BUILD_TAG = os.getenv("BUILD_TAG", "")
    if BUILD_TAG[:1] == "v":
        return BUILD_TAG
    elif BUILD_SHA:
        return f"{BUILD_TAG}({BUILD_SHA[:7]})"
    else:
        return "dev"


def debug_print_exc():
    if DEBUG:
        traceback.print_exc()
